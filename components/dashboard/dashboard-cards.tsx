"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Package, Users, Clock, ArrowUpRight, Activity, Zap } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";

interface DashboardCardsProps {
  organizationId?: string;
}

interface DashboardStats {
  totalProjects: number;
  totalTasks: number;
  completedTasks: number;
  activeUsers: number;
  taskCompletionRate: number;
  recentActivity: number;
}

export function DashboardCards({ organizationId }: DashboardCardsProps) {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Simulated API call - replace with actual data fetching
        const response = await fetch(`/api/dashboard/stats${organizationId ? `?orgId=${organizationId}` : ''}`);
        const data = await response.json();
        setStats(data);
      } catch (error) {
        console.error("Failed to fetch dashboard stats:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [organizationId]);

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-1/3 mb-2" />
              <Skeleton className="h-4 w-2/3" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!stats) return null;

  const cards = [
    {
      title: "Total Projects",
      value: stats.totalProjects,
      description: "Active projects in workspace",
      icon: Package,
      trend: "+2.5%",
      trendUp: true,
    },
    {
      title: "Active Users",
      value: stats.activeUsers,
      description: "Team members online",
      icon: Users,
      trend: "+12%",
      trendUp: true,
    },
    {
      title: "Task Completion",
      value: `${stats.taskCompletionRate}%`,
      description: "Tasks completed this week",
      icon: Clock,
      trend: "-3%",
      trendUp: false,
      showProgress: true,
    },
    {
      title: "Total Tasks",
      value: stats.totalTasks,
      description: "Tasks across all projects",
      icon: Activity,
      trend: "+5%",
      trendUp: true,
    },
    {
      title: "Completed Tasks",
      value: stats.completedTasks,
      description: "Successfully finished tasks",
      icon: ArrowUpRight,
      trend: "+8%",
      trendUp: true,
    },
    {
      title: "Recent Activity",
      value: stats.recentActivity,
      description: "Actions in last 24 hours",
      icon: Zap,
      trend: "+15%",
      trendUp: true,
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {cards.map((card, i) => (
        <Card key={i} className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
            <card.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{card.value}</div>
              <Badge variant={card.trendUp ? "success" : "destructive"} className="text-xs">
                {card.trend}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {card.description}
            </p>
            {card.showProgress && (
              <Progress
                value={stats.taskCompletionRate}
                className="mt-3"
              />
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
