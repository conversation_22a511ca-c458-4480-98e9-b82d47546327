"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartConfig, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, TrendingDown, Activity, Users, Code, Clock } from "lucide-react";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  Line,
  LineChart,
  ResponsiveContainer,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>hart,
  Pie,
  Cell,
} from "recharts";
import { cn } from "@/lib/utils";

interface DashboardChartsProps {
  className?: string;
  organizationId?: string;
}

interface ChartData {
  projectActivity: Array<{
    date: string;
    projects: number;
    tasks: number;
    commits: number;
  }>;
  taskCompletion: Array<{
    week: string;
    completed: number;
    pending: number;
    inProgress: number;
  }>;
  userActivity: Array<{
    hour: string;
    activeUsers: number;
  }>;
  projectDistribution: Array<{
    name: string;
    value: number;
    color: string;
  }>;
}

const chartConfig = {
  projects: {
    label: "Projects",
    color: "hsl(var(--chart-1))",
  },
  tasks: {
    label: "Tasks",
    color: "hsl(var(--chart-2))",
  },
  commits: {
    label: "Commits",
    color: "hsl(var(--chart-3))",
  },
  completed: {
    label: "Completed",
    color: "hsl(var(--chart-1))",
  },
  pending: {
    label: "Pending",
    color: "hsl(var(--chart-2))",
  },
  inProgress: {
    label: "In Progress",
    color: "hsl(var(--chart-3))",
  },
  activeUsers: {
    label: "Active Users",
    color: "hsl(var(--chart-4))",
  },
} satisfies ChartConfig;

export function DashboardCharts({ className, organizationId }: DashboardChartsProps) {
  const [data, setData] = useState<ChartData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchChartData = async () => {
      try {
        // Simulated API call - replace with actual data fetching
        const response = await fetch(`/api/dashboard/charts${organizationId ? `?orgId=${organizationId}` : ''}`);

        if (!response.ok) {
          // Fallback to mock data if API fails
          const mockData: ChartData = {
            projectActivity: [
              { date: "2024-01", projects: 12, tasks: 45, commits: 89 },
              { date: "2024-02", projects: 15, tasks: 52, commits: 102 },
              { date: "2024-03", projects: 18, tasks: 61, commits: 118 },
              { date: "2024-04", projects: 22, tasks: 68, commits: 134 },
              { date: "2024-05", projects: 25, tasks: 75, commits: 145 },
              { date: "2024-06", projects: 28, tasks: 82, commits: 156 },
            ],
            taskCompletion: [
              { week: "Week 1", completed: 23, pending: 12, inProgress: 8 },
              { week: "Week 2", completed: 28, pending: 15, inProgress: 6 },
              { week: "Week 3", completed: 32, pending: 10, inProgress: 9 },
              { week: "Week 4", completed: 35, pending: 8, inProgress: 7 },
            ],
            userActivity: [
              { hour: "00:00", activeUsers: 2 },
              { hour: "04:00", activeUsers: 1 },
              { hour: "08:00", activeUsers: 8 },
              { hour: "12:00", activeUsers: 15 },
              { hour: "16:00", activeUsers: 12 },
              { hour: "20:00", activeUsers: 6 },
            ],
            projectDistribution: [
              { name: "WebVM Projects", value: 35, color: "#8884d8" },
              { name: "Frontend", value: 25, color: "#82ca9d" },
              { name: "Backend", value: 20, color: "#ffc658" },
              { name: "DevOps", value: 15, color: "#ff7300" },
              { name: "Other", value: 5, color: "#00ff00" },
            ],
          };
          setData(mockData);
        } else {
          const chartData = await response.json();
          setData(chartData);
        }
      } catch (error) {
        console.error("Failed to fetch chart data:", error);
        // Set mock data on error
        const mockData: ChartData = {
          projectActivity: [
            { date: "2024-01", projects: 12, tasks: 45, commits: 89 },
            { date: "2024-02", projects: 15, tasks: 52, commits: 102 },
            { date: "2024-03", projects: 18, tasks: 61, commits: 118 },
            { date: "2024-04", projects: 22, tasks: 68, commits: 134 },
            { date: "2024-05", projects: 25, tasks: 75, commits: 145 },
            { date: "2024-06", projects: 28, tasks: 82, commits: 156 },
          ],
          taskCompletion: [
            { week: "Week 1", completed: 23, pending: 12, inProgress: 8 },
            { week: "Week 2", completed: 28, pending: 15, inProgress: 6 },
            { week: "Week 3", completed: 32, pending: 10, inProgress: 9 },
            { week: "Week 4", completed: 35, pending: 8, inProgress: 7 },
          ],
          userActivity: [
            { hour: "00:00", activeUsers: 2 },
            { hour: "04:00", activeUsers: 1 },
            { hour: "08:00", activeUsers: 8 },
            { hour: "12:00", activeUsers: 15 },
            { hour: "16:00", activeUsers: 12 },
            { hour: "20:00", activeUsers: 6 },
          ],
          projectDistribution: [
            { name: "WebVM Projects", value: 35, color: "#8884d8" },
            { name: "Frontend", value: 25, color: "#82ca9d" },
            { name: "Backend", value: 20, color: "#ffc658" },
            { name: "DevOps", value: 15, color: "#ff7300" },
            { name: "Other", value: 5, color: "#00ff00" },
          ],
        };
        setData(mockData);
      } finally {
        setLoading(false);
      }
    };

    fetchChartData();
  }, [organizationId]);

  if (loading) {
    return (
      <div className={cn("space-y-6", className)}>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-32" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[300px] w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!data) {
    return (
      <div className={cn("space-y-6", className)}>
        <Card>
          <CardHeader>
            <CardTitle>Analytics</CardTitle>
            <CardDescription>Unable to load chart data</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center h-[300px] text-muted-foreground">
              No data available
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Project Activity Chart */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Project Activity
            </CardTitle>
            <CardDescription>
              Projects, tasks, and commits over time
            </CardDescription>
          </div>
          <Badge variant="secondary" className="flex items-center gap-1">
            <TrendingUp className="h-3 w-3" />
            +12% this month
          </Badge>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig} className="h-[300px]">
            <AreaChart data={data.projectActivity}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Area
                type="monotone"
                dataKey="projects"
                stackId="1"
                stroke="var(--color-projects)"
                fill="var(--color-projects)"
                fillOpacity={0.6}
              />
              <Area
                type="monotone"
                dataKey="tasks"
                stackId="1"
                stroke="var(--color-tasks)"
                fill="var(--color-tasks)"
                fillOpacity={0.6}
              />
              <Area
                type="monotone"
                dataKey="commits"
                stackId="1"
                stroke="var(--color-commits)"
                fill="var(--color-commits)"
                fillOpacity={0.6}
              />
            </AreaChart>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Task Completion Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Task Completion Trends
          </CardTitle>
          <CardDescription>
            Weekly task completion breakdown
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig} className="h-[300px]">
            <BarChart data={data.taskCompletion}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="week" />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Bar dataKey="completed" fill="var(--color-completed)" />
              <Bar dataKey="inProgress" fill="var(--color-inProgress)" />
              <Bar dataKey="pending" fill="var(--color-pending)" />
            </BarChart>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* User Activity Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            User Activity
          </CardTitle>
          <CardDescription>
            Active users throughout the day
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig} className="h-[200px]">
            <LineChart data={data.userActivity}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="hour" />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Line
                type="monotone"
                dataKey="activeUsers"
                stroke="var(--color-activeUsers)"
                strokeWidth={2}
                dot={{ fill: "var(--color-activeUsers)" }}
              />
            </LineChart>
          </ChartContainer>
        </CardContent>
      </Card>
    </div>
  );
}